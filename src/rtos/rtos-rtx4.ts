/* eslint-disable @typescript-eslint/naming-convention */
import * as vscode from 'vscode';
import { DebugProtocol } from '@vscode/debugprotocol';
import * as RTOSCommon from './rtos-common';

// RTX v4 thread display fields
enum DisplayFields {
    ID,
    Address,
    TaskName,
    Status,
    Priority,
    StackStart,
    StackTop,
    StackSize,
    StackUsed,
    StackFree,
    StackPeak
}

const numType = RTOSCommon.ColTypeEnum.colTypeNumeric;
const RTX4Items: { [key: string]: RTOSCommon.DisplayColumnItem } = {};
RTX4Items[DisplayFields[DisplayFields.ID]] = { width: 1, headerRow1: '', headerRow2: 'ID', colType: numType };
RTX4Items[DisplayFields[DisplayFields.Address]] = {
    width: 3,
    headerRow1: 'Thread',
    headerRow2: 'Address',
    colGapBefore: 1
};
RTX4Items[DisplayFields[DisplayFields.TaskName]] = { width: 4, headerRow1: '', headerRow2: 'Task Name' };
RTX4Items[DisplayFields[DisplayFields.Status]] = { width: 3, headerRow1: '', headerRow2: 'Status' };
RTX4Items[DisplayFields[DisplayFields.Priority]] = {
    width: 1.5,
    headerRow1: 'Prio',
    headerRow2: 'rity',
    colType: numType
};
RTX4Items[DisplayFields[DisplayFields.StackStart]] = {
    width: 3,
    headerRow1: 'Stack',
    headerRow2: 'Start',
    colType: RTOSCommon.ColTypeEnum.colTypeLink,
    colGapBefore: 1
};
RTX4Items[DisplayFields[DisplayFields.StackTop]] = { width: 3, headerRow1: 'Stack', headerRow2: 'Top' };
RTX4Items[DisplayFields[DisplayFields.StackSize]] = {
    width: 2,
    headerRow1: 'Stack',
    headerRow2: 'Size',
    colType: numType
};
RTX4Items[DisplayFields[DisplayFields.StackUsed]] = {
    width: 2,
    headerRow1: 'Stack',
    headerRow2: 'Used',
    colType: numType
};
RTX4Items[DisplayFields[DisplayFields.StackFree]] = {
    width: 2,
    headerRow1: 'Stack',
    headerRow2: 'Free',
    colType: numType
};
RTX4Items[DisplayFields[DisplayFields.StackPeak]] = {
    width: 2,
    headerRow1: 'Stack',
    headerRow2: 'Peak',
    colType: numType
};
const DisplayFieldNames: string[] = Object.keys(RTX4Items);

export class RTOSRTX4 extends RTOSCommon.RTOSBase {
    // RTX v4 global variables and references
    private osRunning: RTOSCommon.RTOSVarHelperMaybe;
    private osTask: RTOSCommon.RTOSVarHelperMaybe;
    private osTaskCurrent: RTOSCommon.RTOSVarHelperMaybe;
    private osTaskCount: RTOSCommon.RTOSVarHelperMaybe;

    private stale = true;
    private foundThreads: RTOSCommon.RTOSThreadInfo[] = [];
    private finalThreads: RTOSCommon.RTOSThreadInfo[] = [];
    private timeInfo = '';
    private readonly maxThreads = 256;
    private helpHtml: string | undefined;

    // Stack growth direction (-1 for downward, 1 for upward)
    private stackIncrements = -1;
    private stackPattern = 0xCCCCCCCC; // Default stack fill pattern

    constructor(public session: vscode.DebugSession) {
        super(session, 'RTX v4');

        // Check for RTX configuration from debug session
        if (session.configuration.rtosViewConfig) {
            if (session.configuration.rtosViewConfig.stackPattern) {
                this.stackPattern = parseInt(session.configuration.rtosViewConfig.stackPattern);
            }
            if (session.configuration.rtosViewConfig.stackGrowth) {
                this.stackIncrements = parseInt(session.configuration.rtosViewConfig.stackGrowth);
            }
        }
    }

    public async tryDetect(useFrameId: number): Promise<RTOSCommon.RTOSBase> {
        this.progStatus = 'stopped';
        try {
            if (this.status === 'none') {
                // Try to detect RTX v4 by looking for key global variables
                // RTX v4 typically has variables like os_running, os_tsk, etc.

                // Try different possible RTX v4 variable names
                this.osRunning = await this.getVarIfEmpty(
                    this.osRunning,
                    useFrameId,
                    'os_running',
                    true
                );

                // Alternative variable names for different RTX v4 versions
                if (!this.osRunning) {
                    this.osRunning = await this.getVarIfEmpty(
                        this.osRunning,
                        useFrameId,
                        'osRtxInfo.kernel.state',
                        true
                    );
                }

                // Look for task-related variables
                this.osTask = await this.getVarIfEmpty(
                    this.osTask,
                    useFrameId,
                    'os_tsk',
                    true
                );

                if (!this.osTask) {
                    this.osTask = await this.getVarIfEmpty(
                        this.osTask,
                        useFrameId,
                        'osRtxInfo.thread.run.curr',
                        true
                    );
                }

                this.osTaskCurrent = await this.getVarIfEmpty(
                    this.osTaskCurrent,
                    useFrameId,
                    'os_tsk_current',
                    true
                );

                this.osTaskCount = await this.getVarIfEmpty(
                    this.osTaskCount,
                    useFrameId,
                    'os_tsk_count',
                    true
                );

                // We need at least one of these variables to detect RTX v4
                if (!this.osRunning && !this.osTask && !this.osTaskCurrent) {
                    throw new Error('RTX v4 not detected - no recognizable global variables found');
                }

                this.status = 'initialized';
            }
            return this;
        } catch (e) {
            if (e instanceof RTOSCommon.ShouldRetry) {
                console.error(e.message);
            } else {
                this.status = 'failed';
                this.failedWhy = e;
            }
            return this;
        }
    }

    public refresh(frameId: number): Promise<void> {
        return new Promise<void>((resolve) => {
            if (this.progStatus !== 'stopped') {
                resolve();
                return;
            }

            const timer = new RTOSCommon.HrTimer();
            this.stale = true;
            this.timeInfo = new Date().toISOString();
            this.foundThreads = [];

            // Start the refresh process
            this.refreshRTXInfo(frameId).then(
                () => {
                    this.stale = false;
                    this.timeInfo += ' in ' + timer.deltaMs() + ' ms';
                    resolve();
                },
                (reason) => {
                    resolve();
                    console.error('RTX v4 refresh() failed: ', reason);
                }
            );
        });
    }

    private async refreshRTXInfo(frameId: number): Promise<void> {
        try {
            // Check if RTX is running
            if (this.osRunning) {
                const runningState = await this.osRunning.getValue(frameId);
                if (!runningState || runningState === '0') {
                    // RTX is not running
                    this.finalThreads = [];
                    return;
                }
            }

            // Get thread information
            await this.getThreadsInfo(frameId);

            // Sort threads by ID or address
            if (this.foundThreads.length > 0) {
                const firstThread = this.foundThreads[0];
                if (firstThread.display['ID'] && firstThread.display['ID'].text !== '??') {
                    this.foundThreads.sort(
                        (a, b) => parseInt(a.display['ID'].text) - parseInt(b.display['ID'].text)
                    );
                } else {
                    this.foundThreads.sort(
                        (a, b) => parseInt(a.display['Address'].text) - parseInt(b.display['Address'].text)
                    );
                }
            }

            this.finalThreads = [...this.foundThreads];
        } catch (e) {
            console.error('RTX v4 refreshRTXInfo() failed: ', e);
            throw e;
        }
    }

    private async getThreadsInfo(frameId: number): Promise<void> {
        // This is a simplified implementation - RTX v4 thread discovery
        // would need to be adapted based on the specific RTX v4 version and configuration

        if (this.osTask) {
            // Try to get current task information
            try {
                const taskInfo = await this.osTask.getVarChildrenObj(frameId);
                if (taskInfo) {
                    const threadInfo = await this.createThreadInfo(taskInfo, frameId, true);
                    if (threadInfo) {
                        this.foundThreads.push(threadInfo);
                    }
                }
            } catch (e) {
                console.log('Failed to get current task info:', e);
            }
        }

        // TODO: Implement full thread list traversal for RTX v4
        // This would require understanding the specific RTX v4 data structures
        // and how threads are linked together in the task lists
    }