{
    "compilerOptions": {
        "module": "commonjs",
        "target": "ES2022",
        "lib": [
            "ES2022",
            "DOM",
            "DOM.Iterable"
        ],
        "outDir": "out",
        "esModuleInterop": true,
        "sourceMap": true,
        "allowJs": true,
        "checkJs": false,
        "skipLibCheck": true,
        // "jsx": "react",
        "forceConsistentCasingInFileNames": true,
        "importHelpers": true,
        "strict": true, /* enable all strict type-checking options */
        /* Additional Checks */
        "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
        // "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
        // "noUnusedParameters": true,  /* Report errors on unused parameters. */
    },
    "exclude": [
        "node_modules",
        ".vscode-test",
        "src/test"
    ]
}