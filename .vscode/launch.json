// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
    "version": "0.2.0",
    "configurations": [
        {
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}"
            ],
            "name": "Launch Extension",
            "outFiles": [
                "${workspaceFolder}/dist/**/*.js"
            ],
            "request": "launch",
            // "debugWebviews": true,
            "type": "extensionHost"
        },
        {
            "name": "Extension Tests",
            "type": "extensionHost",
            "request": "launch",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}",
                "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"
            ],
            "outFiles": [
                "${workspaceFolder}/out/**/*.js",
                "${workspaceFolder}/dist/**/*.js"
            ],
            "preLaunchTask": "tasks: watch-tests"
        }
    ]
}