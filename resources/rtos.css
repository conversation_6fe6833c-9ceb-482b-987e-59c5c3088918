.threads-grid {
  gap: 0;
  text-align: left;
}

.threads-header-row {
  border: 1px solid var(--vscode-panel-border);
}

.threads-row {
  border: 1px solid var(--vscode-panel-border);
}

.threads-header-cell {
  padding: 0px;
  color: var(--vscode-symbolIcon-constructorForeground);
  font-family: var(--vscode-editor-font-family);
  font-weight: var(--vscode-editor-font-weight);
  font-size: var(--vscode-editor-font-size);
}

.threads-cell {
  padding: 0px;
  font-family: var(--vscode-editor-font-family);
  font-weight: var(--vscode-editor-font-weight);
  font-size: var(--vscode-editor-font-size);
}

.whitespacePreserve {
  white-space: pre-wrap
}

vscode-link {
  font-family: var(--vscode-editor-font-family);
  font-weight: var(--vscode-editor-font-weight);
  font-size: var(--vscode-editor-font-size);
}

.running {
  color: var(--vscode-symbolIcon-classForeground);
  font-weight: bold;
}

.backgroundPercent {
  background: linear-gradient(to right, var(--vscode-charts-blue) 0% var(--rtosview-percentage-active, 0%), rgba(0, 0, 0, 0) var(--rtosview-percentage-active, 0%) 100%);
  background-clip: padding-box;
}

.centerAlign {
  text-align: center;
}

.rightAlign {
  text-align: right;
}

.collapse-button {
  color: var(--vscode-textLink-activeForeground);
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
  padding: 0;
  width: 100%;
  border: none;
  text-align: left;
  font-family: var(--vscode-editor-font-family);
  font-weight: var(--vscode-editor-font-weight);
  font-size: var(--vscode-editor-font-size);
  outline: none;
}

.collapse-button:after {
  content: '\002B';
  font-weight: bold;
  float: right;
  margin-left: 5px;
  margin-right: 5px;
}

.collapse-button.active:after {
  content: "\2212";
}

.collapse {
  padding: 0 18px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

.help-button {
  font-style: italic;
  color: var(--vscode-textLink-activeForeground);
  background-color: var(--vscode-textBlockQuote-background);
  cursor: pointer;
  padding: 4px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-family: var(--vscode-editor-font-family);
  font-weight: var(--vscode-editor-font-weight);
  font-size: var(--vscode-editor-font-size);
}

.help-button:after {
  content: '\002B';
  font-weight: bold;
  float: right;
  margin-left: 5px;
}

.help-button.active:after {
  content: "\2212";
}

.help {
  padding: 0 18px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;

  background-color: var(--vscode-textBlockQuote-background);
}
