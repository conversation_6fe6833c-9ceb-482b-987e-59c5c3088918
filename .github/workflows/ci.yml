name: ci

on:
    push:
        branches:
            - main
        tags:
            - '*'
    pull_request:
        branches:
            - main

jobs:
    setup:
        name: Setup
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Cache NPM Install
              id: cache-npm
              uses: actions/cache@v4
              with:
                  path: ./node_modules
                  key: npm-${{ hashFiles('./package-lock.json') }}
            - name: Install NPM dependencies
              if: steps.cache-npm.outputs.cache-hit != 'true'
              run: |
                  npm install

    build:
        name: Build
        needs: setup
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Load NPM install
              id: cache-npm
              uses: actions/cache@v4
              with:
                  path: ./node_modules
                  key: npm-${{ hashFiles('./package-lock.json') }}
            - name: Package Binary
              run: npm run package
            - name: Upload Artifact
              uses: actions/upload-artifact@v4
              with:
                  name: vsix-package
                  path: ./*.vsix
                  retention-days: 10

    lint:
        name: Lint
        needs: setup
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Load NPM install
              id: cache-npm
              uses: actions/cache@v4
              with:
                  path: ./node_modules
                  key: npm-${{ hashFiles('./package-lock.json') }}
            - name: Lint Project
              run: npm run lint
